part of xr_helper;

class GetStorageService {
  static const String _boxName = 'calendar_app_storage';

  static final _box = GetSecureStorage(container: _boxName, password: '123456');

  static Future<void> init() async {
    await GetSecureStorage.init(container: _boxName, password: '123456');
  }

  static Future<bool> setData(
      {required String key, required dynamic value}) async {
    try {
      await _box.write(key, value);

      // Data saved - minimal logging to reduce storage usage
      return true;
    } on Exception catch (e) {
      Log.e("Error while saving local data $e");
      return false;
    }
  }

  static dynamic getData({required dynamic key}) {
    return _box.read(key);
  }

  Future<bool> removeLocalData({required String key}) async {
    try {
      await _box.remove(key);

      // Data removed - minimal logging
      return true;
    } on Exception catch (e) {
      Log.e("Error while removing local data $e");
      return false;
    }
  }

  static bool hasData({required String key}) {
    return _box.hasData(key);
  }

  static Future<bool> clearLocalData() async {
    try {
      await _box.erase();

      // Data cleared - minimal logging
      return true;
    } on Exception catch (e) {
      Log.e("Error while clearing local data $e");
      return false;
    }
  }

  //? Remove Key
  static Future<bool> removeKey({required String key}) async {
    try {
      await _box.remove(key);

      // Data removed - minimal logging
      return true;
    } on Exception catch (e) {
      Log.e("Error while removing local data $e");
      return false;
    }
  }
}
