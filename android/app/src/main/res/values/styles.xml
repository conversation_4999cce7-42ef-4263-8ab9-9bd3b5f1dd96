<resources>
    <!-- Launch theme for splash screen -->
    <style name="LaunchTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">@drawable/launch_background</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>

    <!-- Main app theme -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="android:windowBackground">?android:colorBackground</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>

    <!-- Normal app theme without ActionBar -->
    <style name="NormalTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowBackground">?android:colorBackground</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>

    <!-- Text styles -->
    <style name="NormalTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:gravity">center</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>

    <style name="HighlightedTextStyle">
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/yellow</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="fontFamily">@font/tajawal_regular</item>
    </style>
</resources>


