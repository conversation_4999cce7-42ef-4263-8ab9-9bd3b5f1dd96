## Keep all classes in the com.perfectfit.qurankareem package and its subpackages
#-keep class com.perfectfit.qurankareem.** { *; }
#
## Do not warn about missing classes in the com.perfectfit.qurankareem.lib package and its subpackages
#-dontwarn com.perfectfit.qurankareem.lib.**
#
## Keep all Flutter classes
#-keep class io.flutter.app.** { *; }
#-keep class io.flutter.embedding.** { *; }
#-keep class io.flutter.plugin.** { *; }
#
## Keep all classes in the Dart package
#-keep class io.flutter.plugins.** { *; }
#
## Keep all classes in the Kotlin package
#-keep class kotlin.** { *; }
#
## Keep all classes in the AndroidX package
#-keep class androidx.** { *; }
#-dontwarn androidx.**
#
## Keep AndroidX Core classes specifically
#-keep class androidx.core.** { *; }
#-keep class androidx.core.content.** { *; }
#-keep class androidx.core.app.** { *; }
#
## Keep all classes in the Firebase package
#-keep class com.google.firebase.** { *; }
#
## Keep all classes in the WorkManager package
#-keep class androidx.work.** { *; }
#-dontwarn androidx.work.**
#
## Keep all classes in the Awesome Notifications package
#-keep class me.carda.awesome_notifications.** { *; }
#-keep class me.carda.awesome_notifications.core.** { *; }
#-keep class me.carda.awesome_notifications.core.broadcasters.** { *; }
#-keep class me.carda.awesome_notifications.core.broadcasters.receivers.** { *; }
#
## Prevent obfuscation of awesome_notifications receivers
#-keep public class me.carda.awesome_notifications.core.broadcasters.receivers.RefreshSchedulesReceiver
#-keep public class me.carda.awesome_notifications.core.broadcasters.receivers.ScheduledNotificationReceiver
#
## Keep constructors for receivers and ensure they can be instantiated
#-keepclassmembers class me.carda.awesome_notifications.core.broadcasters.receivers.** {
#    public <init>();
#    public <init>(...);
#}
#
## Keep all constructors for reflection-based instantiation
#-keepclassmembers class * extends android.content.BroadcastReceiver {
#    public <init>();
#}
#
#-keepclassmembers class * extends android.app.Service {
#    public <init>();
#}
#
## Keep all classes in the AudioService package - prevent SIGSEGV crashes
#-keep class com.ryanheise.audioservice.** { *; }
#-keep class com.ryanheise.just_audio.** { *; }
#-keep class com.ryanheise.just_audio_background.** { *; }
#
## Prevent obfuscation of audio service methods that use reflection
#-keepclassmembers class com.ryanheise.audioservice.** {
#    public <init>(...);
#    public void onStart(...);
#    public void onStop(...);
#    public void onPause(...);
#    public void onPlay(...);
#}
#
## Keep native audio methods
#-keepclassmembers class * {
#    native <methods>;
#}
#
## Keep all classes in the HomeWidget package
#-keep class com.perfectfit.qurankareem.** { *; }
#
## Keep widget classes specifically - prevent instantiation crashes
#-keep class com.perfectfit.qurankareem.AndroidAzanWidget { *; }
#-keep class com.perfectfit.qurankareem.AndroidAzanWidget4x1 { *; }
#-keep class com.perfectfit.qurankareem.PrayerUpdateReceiver { *; }
#
## Keep widget constructors and prevent reflection issues
#-keepclassmembers class com.perfectfit.qurankareem.AndroidAzanWidget {
#    public <init>();
#    public <init>(...);
#}
#-keepclassmembers class com.perfectfit.qurankareem.AndroidAzanWidget4x1 {
#    public <init>();
#    public <init>(...);
#}
#-keepclassmembers class com.perfectfit.qurankareem.PrayerUpdateReceiver {
#    public <init>();
#    public <init>(...);
#}
#
## Prevent obfuscation of widget provider methods
#-keepclassmembers class * extends android.appwidget.AppWidgetProvider {
#    public void onUpdate(android.content.Context, android.appwidget.AppWidgetManager, int[]);
#    public void onReceive(android.content.Context, android.content.Intent);
#}
#
## Prevent obfuscation of broadcast receiver methods
#-keepclassmembers class * extends android.content.BroadcastReceiver {
#    public void onReceive(android.content.Context, android.content.Intent);
#}
#
## Keep all classes in the CircularCountDownTimer package
#-keep class com.perfectfit.qurankareem.CircularCountDownTimer.** { *; }
#
## Keep all classes in the HomeBanner package
#-keep class com.perfectfit.qurankareem.HomeBanner.** { *; }
#
## Keep all classes in the MainScreen package
#-keep class com.perfectfit.qurankareem.MainScreen.** { *; }
#
## Google Play Core classes removed due to SDK 34 compatibility
## -keep class com.google.android.play.core.** { *; }
## -dontwarn com.google.android.play.core.**
#
## Additional rules for reflection and instantiation
#-keepattributes Signature
#-keepattributes *Annotation*
#-keepattributes EnclosingMethod
#-keepattributes InnerClasses
#
## Removed overly broad rules that can cause Play Store issues
## -keep class * {
##     public <init>();
## }
## -keep class * extends java.lang.Object {
##     public <init>();
## }
#
## Keep all enum classes
#-keepclassmembers enum * {
#    public static **[] values();
#    public static ** valueOf(java.lang.String);
#}