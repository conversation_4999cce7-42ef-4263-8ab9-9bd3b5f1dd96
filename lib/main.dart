import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/app.dart';
import 'package:quran_broadcast_app/src/core/shared/utils/initialize.dart';
import 'package:xr_helper/xr_helper.dart';

void main() async {
  // Add global error handling to prevent crashes
  FlutterError.onError = (FlutterErrorDetails details) {
    Log.e('Flutter Error: ${details.exception}');
    Log.e('Stack trace: ${details.stack}');
  };

  // Handle errors in async operations
  PlatformDispatcher.instance.onError = (error, stack) {
    Log.e('Platform Error: $error');
    Log.e('Stack trace: $stack');
    return true; // Prevent crash
  };

  WidgetsFlutterBinding.ensureInitialized();

  try {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  } catch (e) {
    Log.e('SystemChrome Error: $e');
  }

  try {
    await initialize();
    Log.i('App initialization completed');
  } catch (e) {
    Log.e('Initialization Error: $e');
    // Continue app startup even if initialization fails partially
  }

  runApp(
    const ProviderScope(
      child: BaseApp(),
    ),
  );
}
