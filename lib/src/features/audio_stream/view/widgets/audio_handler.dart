import 'package:audio_service/audio_service.dart';

import '../audio_stream_screen.dart';

class MyAudioHandler extends BaseAudioHandler with <PERSON><PERSON><PERSON><PERSON><PERSON>, SeekHandler {
  MyAudioHandler() {
    globalAudioPlayer.currentIndexStream.listen((index) {
      if (index != null && index < queue.value.length) {
        mediaItem.add(queue.value[index]);
      }
    });
  }

  @override
  Future<void> play() async {
    await globalAudioPlayer.play();
    globalIsPlaying.value = true;
  }

  @override
  Future<void> pause() async {
    await globalAudioPlayer.pause();
    playbackState.add(playbackState.value.copyWith(playing: false));
    globalIsPlaying.value = false;
  }

  @override
  Future<void> stop() async {
    await globalAudioPlayer.stop();

    playbackState.add(playbackState.value.copyWith(playing: false));

    globalIsPlaying.value = false;
  }

  Future<void> showLiveAudioNotification(
    String url,
  ) async {
    final mediaItem = MediaItem(
      id: '1',
      title: "البث المباشر",
      album: "تقويم إذاعة القرآن الكريم",
      artUri: Uri.parse(url),
      isLive: true,
    );

    this.mediaItem.add(mediaItem);

    // queue.add([mediaItem]);

    // await globalAudioPlayer.setAudioSource(AudioSource.uri(
    //   Uri.parse(url),
    //   tag: mediaItem,
    // ));
  }

  Future<void> setUrl(
    String url,
  ) async {
    globalIsInitialing.value = true;

    await globalAudioPlayer.setUrl(
      url,
      tag: MediaItem(
        id: '1',
        title: "البث المباشر",
        album: "تقويم إذاعة القرآن الكريم",
        artUri: Uri.parse(url),
        isLive: true,
      ),
      preload: false,
    );

    await globalAudioPlayer.stop();

    globalIsInitialing.value = false;

    // await globalAudioPlayer.setAudioSource(AudioSource.uri(
    //   Uri.parse(url),
    //   tag: mediaItem,
    // ));
  }
}
