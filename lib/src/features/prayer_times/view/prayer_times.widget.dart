import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/utils/show_notify_prayer_times_sheet.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/next_previous_widget/next_previous.widget.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/home/<USER>/widgets/next_prayer_times.dart';
import 'package:quran_broadcast_app/src/features/notifications/controller/notification_controller.dart';
import 'package:xr_helper/xr_helper.dart';

class PrayerTimesWidget extends HookConsumerWidget {
  const PrayerTimesWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationController =
        ref.watch(notificationControllerProvider(ref));
    final calendarController = ref.watch(calendarControllerNotifierProvider);
    final currentDay = calendarController
        .calendarByDate(calendarController.currentDateNotifier.value);

    final isTablet = context.isTablet;

    final isCurrentDayToday =
        currentDay.gregorianDate == DateTime.now().formatDateToString;

    final isFirstDayInCalendarDays = CalendarController
            .calendar.value.firstOrNull?.days.firstOrNull?.gregorianDate ==
        calendarController.currentDateNotifier.value.formatDateToString;

    final isLastDayInCalendarDays = CalendarController
            .calendar.value.lastOrNull?.days.lastOrNull?.gregorianDate ==
        calendarController.currentDateNotifier.value.formatDateToString;

    useEffect(() {
      // Ensure the app always navigates to the current date on widget build
      final now = DateTime.now();

      if (calendarController.currentDateNotifier.value.formatDateToString !=
          now.formatDateToString) {
        calendarController.changeDateByDateTime(now);
      }

      // Optimize timer to reduce memory pressure and prevent crashes
      Timer? timer;

      void scheduleNextMidnightCheck() {
        timer?.cancel(); // Cancel any existing timer

        final now = DateTime.now();
        final nextMidnight = DateTime(now.year, now.month, now.day + 1);
        final timeUntilMidnight = nextMidnight.difference(now);

        // Schedule a single timer for midnight instead of checking every minute
        timer = Timer(timeUntilMidnight, () {
          try {
            final newNow = DateTime.now();
            calendarController.changeDateByDateTime(newNow);
            // Schedule the next midnight check
            scheduleNextMidnightCheck();
          } catch (e) {
            Log.e('PrayerTimesWidget timer error: $e');
          }
        });
      }

      // Start the optimized midnight checking
      scheduleNextMidnightCheck();

      return () {
        timer?.cancel();
        timer = null;
      };
    }, []);

//     useEffect(() {
//       // Ensure the app always navigates to the current date on widget build
//       final now = DateTime.now();
//
//       if (calendarController.currentDateNotifier.value.formatDateToString !=
//           now.formatDateToString) {
//         calendarController.changeDateByDateTime(now);
//       }
//
//       // Periodically check for date change at midnight
//       final timer = Timer.periodic(const Duration(minutes: 1), (timer) {
//         final now = DateTime.now();
//         if ((now.hour == 23 && now.minute == 59)) {
//           calendarController
//               .changeDateByDateTime(now.add(const Duration(days: 1)));
//         } else if ((now.hour == 0 && now.minute == 0) ||
//             (now.hour == 0 && now.minute == 1)) {
//           calendarController.changeDateByDateTime(now);
//         }
//       });
//
//       return timer.cancel;
//     }, []);

    return Stack(
      children: [
        Container(
          height: isTablet ? 150 : null,
          padding: const EdgeInsets.all(AppSpaces.padding8),
          margin: const EdgeInsets.all(AppSpaces.screenPadding),
          decoration: BoxDecoration(
            color: ColorManager.grey,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                spreadRadius: 0,
                blurRadius: 10,
                offset: const Offset(0, .5),
              ),
            ],
          ),
          child: FittedBox(
            fit: isTablet ? BoxFit.contain : BoxFit.scaleDown,
            child: currentDay.prayerTimes.toJson().values.toList().isEmpty
                ? const Padding(
                    padding: EdgeInsets.all(AppSpaces.padding12),
                    child: Text('لا يوجد أوقات صلاة لهذا اليوم'),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children:
                        List.generate(AppConsts.prayerNames.length, (index) {
                      final prayerTime = currentDay.prayerTimes
                          .toJson()
                          .values
                          .toList()[index];

                      return HookBuilder(builder: (context) {
                        final isNotificationEnabled =
                            notificationController.notificationStates[index];

                        final isSelected = useState(isNotificationEnabled);

                        final isPrayTime =
                            getNextPrayerTime(currentDay.prayerTimes).name ==
                                    AppConsts.prayerNames[index] &&
                                isCurrentDayToday;

                        final hour = int.parse(prayerTime.isEmpty
                            ? '0'
                            : prayerTime.split(':')[0]);
                        final pmOrAm = hour >= 12 ? 'م' : 'ص';

                        final formattedTime = TimeOfDay(
                          hour: hour == 12
                              ? 12
                              : hour > 12
                                  ? hour - 12
                                  : hour,
                          minute: int.parse(prayerTime.isEmpty
                              ? '0'
                              : prayerTime.split(':')[1]),
                        );

                        final formattedMinute =
                            formattedTime.minute.toString().padLeft(2, '0');

                        final isCurrentTimeIsShorouk =
                            getNextPrayerTime(currentDay.prayerTimes).name ==
                                    'الشروق' &&
                                isCurrentDayToday &&
                                AppConsts.prayerNames[index] == 'الشروق';

                        return Row(
                          children: [
                            if (index != 0) ...[
                              AppGaps.gap4,
                              Container(
                                width: .5,
                                height: 40,
                                color: Colors.grey,
                              ),
                              AppGaps.gap4,
                            ],
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    showNotifyPrayerTimesSheet(
                                      context,
                                      ref: ref,
                                    );
                                  },
                                  child: CircleAvatar(
                                    backgroundColor: isSelected.value
                                        ? ColorManager.primaryColor
                                        : ColorManager.blueGrey,
                                    child: Icon(
                                      isSelected.value
                                          ? Icons.notifications_outlined
                                          : Icons.notifications_off_outlined,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                                AppGaps.gap4,
                                Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        color: isCurrentTimeIsShorouk
                                            ? ColorManager.lightPrimaryColor
                                            : isPrayTime
                                                ? ColorManager.primaryColor
                                                : Colors.transparent,
                                      ),
                                      child: Column(
                                        children: [
                                          Text(
                                            AppConsts.prayerNames[index],
                                            style:
                                                AppTextStyles.subTitle.copyWith(
                                              fontWeight: FontWeight.bold,
                                              color: isCurrentTimeIsShorouk
                                                  ? ColorManager.primaryColor
                                                  : isPrayTime
                                                      ? Colors.white
                                                      : Colors.black,
                                            ),
                                          ),
                                          AppGaps.gap4,
                                          Text(
                                              '${formattedTime.hour}:$formattedMinute $pmOrAm',
                                              style: AppTextStyles.subTitle
                                                  .copyWith(
                                                color: isCurrentTimeIsShorouk
                                                    ? ColorManager.primaryColor
                                                    : isPrayTime
                                                        ? Colors.white
                                                        : Colors.black,
                                                fontWeight: FontWeight.bold,
                                              )),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            AppGaps.gap2,
                          ],
                        );
                      });
                    }),
                  ),
          ),
        ).paddingSymmetric(horizontal: AppSpaces.padding8),
        Positioned.fill(
          child: NextPreviousWidget(
            isSpaceBetween: true,
            circleAvatarRadius: 14,
            onNext: isLastDayInCalendarDays
                ? null
                : () {
                    calendarController.changeDate(1);
                  },
            onPrevious: isFirstDayInCalendarDays
                ? null
                : () {
                    calendarController.changeDate(-1);
                  },
          ).paddingSymmetric(horizontal: AppSpaces.padding8),
        )
      ],
    );
  }
}
