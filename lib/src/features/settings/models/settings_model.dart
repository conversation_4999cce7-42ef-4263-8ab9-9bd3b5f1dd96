class SettingsModel {
  final AboutUsModel aboutUs;
  final StaticModel live;
  final StaticModel android;
  final StaticModel apple;
  final List<SocialModel> socials;
  final List<int> years;
  final String? firstYear;
  final String? lastYear;

  final DateTime? lastUpdatedAt;

  SettingsModel({
    required this.aboutUs,
    required this.live,
    required this.android,
    required this.apple,
    required this.socials,
    this.years = const [],
    this.lastUpdatedAt,
    this.firstYear,
    this.lastYear,
  });

  factory SettingsModel.empty() {
    return SettingsModel(
      aboutUs: AboutUsModel(),
      live: StaticModel(),
      android: StaticModel(),
      apple: StaticModel(),
      socials: [],
      years: [],
      lastUpdatedAt: DateTime.now(),
      firstYear: '',
      lastYear: '',
    );
  }

  factory SettingsModel.fromJson(Map<String, dynamic> json) {
    return SettingsModel(
      aboutUs: json['about_us'] != null && json['about_us'].isNotEmpty
          ? AboutUsModel.fromJson(json['about_us'][0])
          : AboutUsModel(),
      live: json['static']['live'] != null && json['static']['live'].isNotEmpty
          ? StaticModel.fromJson(json['static']['live'][0])
          : StaticModel(),
      android: json['static']['android'] != null &&
              json['static']['android'].isNotEmpty
          ? StaticModel.fromJson(json['static']['android'][0])
          : StaticModel(),
      apple:
          json['static']['apple'] != null && json['static']['apple'].isNotEmpty
              ? StaticModel.fromJson(json['static']['apple'][0])
              : StaticModel(),
      socials: ((json['socials'] as List?) ?? [])
          .map((item) => SocialModel.fromJson(item))
          .toList(),
      lastUpdatedAt: 
          json['last_updated_at'] == null || json['last_updated_at'].isEmpty
              ? null
              : DateTime.parse(json['last_updated_at']),
      years: json['years'] != null && json['years'].isNotEmpty
          ? List<int>.from(json['years'].map((x) => x['gregorian_year']))
          : [],
      firstYear: json['first_prayer_times'] == null ||
              json['first_prayer_times'].isEmpty
          ? null
          : json['first_prayer_times'],
      lastYear:
          json['last_prayer_times'] == null || json['last_prayer_times'].isEmpty
              ? null
              : json['last_prayer_times'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'about_us': [aboutUs.toJson()],
      'static': {
        'live': [live.toJson()],
        'android': [android.toJson()],
        'apple': [apple.toJson()],
      },
      'socials': socials.map((item) => item.toJson()).toList(),
      'last_updated_at': lastUpdatedAt?.toIso8601String(),
      'years': years.map((x) => {'gregorian_year': x}).toList(),
      'first_prayer_times': firstYear,
      'last_prayer_times': lastYear,
    };
  }
}

class AboutUsModel {
  final String title;
  final String content;

  AboutUsModel({
    this.title = '',
    this.content = '',
  });

  factory AboutUsModel.fromJson(Map<String, dynamic> json) {
    final title = json['title'] == null || json['title'].isEmpty
        ? ''
        : json['title']['ar'];
    final content = json['content'] == null || json['content'].isEmpty
        ? ''
        : json['content']['ar'];

    return AboutUsModel(
      title: title,
      content: content,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title.isEmpty ? null : {'ar': title},
      'content': content.isEmpty ? null : {'ar': content},
    };
  }
}

class StaticModel {
  final String name;
  final String url;
  final String icon;

  StaticModel({
    this.name = '',
    this.url = '',
    this.icon = '',
  });

  factory StaticModel.fromJson(Map<String, dynamic> json) {
    return StaticModel(
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      icon: json['icon'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'url': url,
      'icon': icon,
    };
  }
}

class SocialModel {
  final String name;
  final String url;
  final String icon;

  SocialModel({
    this.name = '',
    this.url = '',
    this.icon = '',
  });

  factory SocialModel.fromJson(Map<String, dynamic> json) {
    return SocialModel(
      name: json['name'] ?? '',
      url: json['url'] ?? '',
      icon: json['icon'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'url': url,
      'icon': icon,
    };
  }
}
