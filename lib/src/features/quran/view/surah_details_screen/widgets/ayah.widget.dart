import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:quran/quran.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/surah_details.screen.dart';
import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/widgets/basmallah.dart';
import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/widgets/quran_header_widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../core/shared/services/connectivity_internet/connectivity_internet.dart';

class AyahWidget extends StatelessWidget {
  final int index;
  final ValueNotifier<String> selectedSpan;
  final ValueNotifier<AudioPlayer> audioPlayer;
  final List<GlobalKey> richTextKeys;
  final String highlightVerse;

  const AyahWidget({
    super.key,
    required this.index,
    required this.selectedSpan,
    required this.highlightVerse,
    required this.richTextKeys,
    required this.audioPlayer,
  });

  @override
  Widget build(BuildContext context) {
    final isLandScape =
        MediaQuery.orientationOf(context) == Orientation.landscape;

    double getFontSize(int index) {
      if (index == 1 || index == 2) {
        return 28.sp;
      } else if (index == 50) {
        return 23.sp;
      } else if (index == 51) {
        return 23.2.sp;
      } else if (index == 77) {
        return 23.sp;
      } else if (index == 145 || index == 201) {
        return 22.4.sp;
      } else if (index == 532 || index == 533) {
        return 22.5.sp;
      } else {
        return 23.1.sp;
      }
    }

    double getTextHeight(int index) {
      if (isLandScape) {
        return 3.5.h;
      }
      if (index == 1 || index == 2) {
        return 2.h;
      } else {
        return 1.95.h;
      }
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: SizedBox(
        width: double.infinity,
        child: RichText(
          key: richTextKeys[index - 1],
          textDirection: TextDirection.rtl,
          textAlign: TextAlign.center,
          softWrap: true,
          locale: const Locale("ar"),
          text: TextSpan(
            children: getPageData(index).expand((e) {
              List<InlineSpan> spans = [];
              for (var i = e["start"]; i <= e["end"]; i++) {
                if (i == 1) {
                  spans.add(WidgetSpan(
                    child: QuranHeaderWidget(
                      e: e,
                    ),
                  ));
                  if (index != 187 && index != 1) {
                    spans.add(const WidgetSpan(
                      child: BasmallahTextWidget(index: 0),
                    ));
                  }

                  if (index == 187) {
                    spans.add(WidgetSpan(
                      child: Container(
                        height: 10.h,
                      ),
                    ));
                  }
                }

                final isSelected = selectedSpan.value == " ${e["surah"]}$i";
                final isBookmark = GetStorageService.getData(
                          key: LocalKeys.favoriteVersePage,
                        ) ==
                        index &&
                    GetStorageService.getData(
                          key: LocalKeys.favoriteVerse,
                        ) ==
                        i;

                final currentVerseNumber = i.toString();
                final isHighlighted =
                    highlightVerse == currentVerseNumber || isBookmark;

                spans.add(TextSpan(
                  recognizer: TapGestureRecognizer()
                    ..onTapUp = (details) {
                      if (selectedSpan.value.isNotEmpty) {
                        selectedSpan.value = "";
                        currentAyahPopup?.remove();
                        currentAyahPopup = null;
                        return;
                      }
                      _showPopupMenu(
                        context,
                        details.globalPosition,
                        e["surah"],
                        i,
                        selectedSpan,
                      );
                      selectedSpan.value = " ${e["surah"]}$i";
                    },
                  text: i == e["start"]
                      ? "${getVerseQCF(e["surah"], i).replaceAll(" ", "").substring(0, 1)}\u200A${getVerseQCF(e["surah"], i).replaceAll(" ", "").substring(1)}"
                      : getVerseQCF(e["surah"], i).replaceAll(' ', ''),
                  style: TextStyle(
                    color: isSelected
                        ? ColorManager.primaryColor
                        : isHighlighted
                            ? ColorManager.secondaryColor
                            : Colors.black,
                    height: getTextHeight(index),
                    letterSpacing: 0.w,
                    wordSpacing: 0,
                    fontFamily: "QCF_P${index.toString().padLeft(3, "0")}",
                    fontSize: getFontSize(index),
                    backgroundColor: Colors.transparent,
                  ),
                ));
              }
              return spans;
            }).toList(),
          ),
        ),
      ),
    );
  }

  void _showPopupMenu(BuildContext context, Offset position, int surah,
      int verse, ValueNotifier<String> selectedSpan) {
    final overlay = Overlay.of(context);

    currentAyahPopup?.remove();
    currentAyahPopup = null;
    selectedSpan.value = "";

    currentAyahPopup = OverlayEntry(
      builder: (context) => Positioned(
        left: position.dx - 50,
        top: position.dy - 80,
        child: HookBuilder(builder: (context) {
          final isBookmarked = useState(GetStorageService.getData(
                    key: LocalKeys.favoriteVersePage,
                  ) ==
                  index &&
              GetStorageService.getData(
                    key: LocalKeys.favoriteVerse,
                  ) ==
                  verse);
          final isPlaying = useState(false);

          Future<void> startAudio() async {
            try {
              // await audioPlayer.value.stop();

              isPlaying.value = true;
              await audioPlayer.value.setUrl(
                getAudioURLByVerse(surah, verse, "ar.minshawi"),
                tag: MediaItem(
                  id: '${getAudioURLByVerse(surah, verse, "ar.minshawi").hashCode}',
                  title: "سورة ${getSurahName(surah)} - آية $verse",
                  album: "القرآن الكريم",
                ),
              );

              await audioPlayer.value.play();

              audioPlayer.value.playerStateStream.listen((event) {
                if (event.processingState == ProcessingState.completed) {
                  isPlaying.value = false;
                }
              });
            } catch (e) {
              debugPrint('Error starting audio: $e');
            }
          }

          Future<void> stopAudio() async {
            try {
              await audioPlayer.value.stop();
              isPlaying.value = false;
            } catch (e) {
              debugPrint('Error stopping audio: $e');
            }
          }

          return Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 2,
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.0),
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 4.0,
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  NetworkAwareWidget(
                    onlineChild: IconButton(
                      icon: Icon(
                        isPlaying.value
                            ? CupertinoIcons.stop
                            : CupertinoIcons.play_arrow,
                        color: isPlaying.value
                            ? ColorManager.errorColor
                            : Colors.black,
                      ),
                      onPressed: isPlaying.value ? stopAudio : startAudio,
                    ),
                    offlineChild: const SizedBox.shrink(),
                  ),
                  IconButton(
                    icon: Icon(
                      isBookmarked.value
                          ? CupertinoIcons.bookmark_fill
                          : CupertinoIcons.bookmark,
                      color: isBookmarked.value
                          ? ColorManager.secondaryColor
                          : Colors.black,
                    ),
                    onPressed: () {
                      if (isBookmarked.value) {
                        GetStorageService.removeKey(
                          key: LocalKeys.favoriteVersePage,
                        );
                        GetStorageService.removeKey(
                          key: LocalKeys.favoriteVerse,
                        );
                      } else {
                        GetStorageService.setData(
                            key: LocalKeys.favoriteVersePage, value: index);
                        GetStorageService.setData(
                            key: LocalKeys.favoriteVerse, value: verse);
                      }

                      isBookmarked.value = !isBookmarked.value;
                    },
                  ),
                  // IconButton(
                  //   icon: const Icon(Icons.share),
                  //   onPressed: share,
                  // ),
                ],
              ),
            ),
          );
        }),
      ),
    );

    overlay.insert(currentAyahPopup!);
  }
}
