import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:just_audio/just_audio.dart';
import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/widgets/ayah.widget.dart';
import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/widgets/top_section.widget.dart';
import 'package:xr_helper/xr_helper.dart';

class SurahDetailsWidget extends HookWidget {
  final int index;
  final ValueNotifier<String> selectedSpan;
  final bool isFullScreen;
  final ValueNotifier<AudioPlayer> audioPlayer;
  final List<GlobalKey> richTextKeys;
  final String highlightVerse;
  final Function? onBack;

  const SurahDetailsWidget({
    super.key,
    required this.index,
    required this.selectedSpan,
    required this.isFullScreen,
    required this.highlightVerse,
    required this.richTextKeys,
    required this.audioPlayer,
    required this.onBack,
  });

  @override
  Widget build(BuildContext context) {
    final isLandScape =
        MediaQuery.orientationOf(context) == Orientation.landscape;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.transparent,
      body: SafeArea(
        top: false,
        // Platform.isAndroid,
        right: Platform.isAndroid,
        left: Platform.isAndroid,
        bottom: Platform.isAndroid,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Column(
              children: [
                if (Platform.isAndroid || isLandScape)
                  Padding(
                    padding: EdgeInsets.only(
                        top: index == 77 ? 0 : AppSpaces.padding8,
                        left: AppSpaces.padding12,
                        right: AppSpaces.padding12),
                    child: TopSectionWidget(
                      index: index,
                      onBack: onBack,
                    ),
                  ),
                Flexible(
                  child: Center(
                    child: Padding(
                      padding: EdgeInsets.only(
                          bottom: index == 77 && Platform.isIOS
                              ? 30.h
                              : Platform.isIOS
                                  ? 20.h
                                  : 0),
                      child: Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          Container(
                            padding: EdgeInsets.only(right: 12.0.w, left: 12.w),
                            decoration: const BoxDecoration(
                              image: DecorationImage(
                                image: AssetImage(
                                    'assets/images/quran_background.png'),
                                fit: BoxFit.fill,
                              ),
                            ),
                            child: Transform.scale(
                              scale: index == 77
                                  ? (Platform.isIOS ? 0.88 : 0.85)
                                  : 0.88,
                              child: SingleChildScrollView(
                                child: AyahWidget(
                                  index: index,
                                  selectedSpan: selectedSpan,
                                  richTextKeys: richTextKeys,
                                  highlightVerse: highlightVerse,
                                  audioPlayer: audioPlayer,
                                ),
                              ),
                            ),
                          ),
                          Text(
                            NumberFormat('#.##', 'ar_EG').format(index),
                            style: AppTextStyles.labelSmall.copyWith(
                              color: Colors.black87,
                              fontSize: isLandScape ? 15 : 15.sp.toDouble(),
                              fontFamily: 'Amiri',
                            ),
                          ).paddingOnly(
                              bottom: isLandScape
                                  ? 0
                                  : index == 1 || index == 2
                                      ? 2.h
                                      : 10.h),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            if (Platform.isIOS && !isLandScape)
              Padding(
                padding: EdgeInsets.only(
                  left: AppSpaces.padding20,
                  right: AppSpaces.padding20,
                  bottom: index == 77 ? AppSpaces.padding4 : AppSpaces.padding8,
                ),
                child: TopSectionWidget(
                  index: index,
                  onBack: onBack,
                ),
              )
          ],
        ),
      ),
    );
  }
}
