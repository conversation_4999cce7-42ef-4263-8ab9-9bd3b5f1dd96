import 'dart:developer';

import 'package:flutter/material.dart'; // import 'package:quran/dart';
import 'package:quran/quran.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/lists/base_list.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/quran/view/surah_details_screen/surah_details.screen.dart';
import 'package:xr_helper/xr_helper.dart';

class AyaFilteredListWidget extends StatelessWidget {
  final Map ayatFiltered;

  const AyaFilteredListWidget({
    super.key,
    required this.ayatFiltered,
  });

  @override
  Widget build(BuildContext context) {
    return BaseList(
      padding: EdgeInsets.zero,
      data: ayatFiltered["result"],
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () async {
            log('afafasf ${ayatFiltered["result"][index]["surah"]}');

            SurahDetailsScreen(
                    highlightVerse:
                        ayatFiltered["result"][index]["verse"].toString(),
                    pageNumber: getPageNumber(
                            ayatFiltered["result"][index]["surah"],
                            ayatFiltered["result"][index]["verse"])
                        .toInt())
                .navigate;
          },
          child: Container(
            padding: const EdgeInsets.all(AppSpaces.padding8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(14),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                            text:
                                "سورة ${getSurahNameArabic(ayatFiltered["result"][index]["surah"])}",
                            style: AppTextStyles.title.copyWith(
                                color: ColorManager.secondaryColor,
                                fontSize: 16)),
                        const TextSpan(
                          text: " - ",
                          style: TextStyle(color: Colors.black, fontSize: 17),
                        ),
                        TextSpan(
                            text: getVerse(
                                ayatFiltered["result"][index]["surah"],
                                ayatFiltered["result"][index]["verse"],
                                verseEndSymbol: true),
                            style: AppTextStyles.subTitle.copyWith(
                              fontFamily: "arsura",
                            )),
                        // verse number
                        const TextSpan(
                          text: " - ",
                          style: TextStyle(color: Colors.black, fontSize: 17),
                        ),
                      ],
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                ),
                AppGaps.gap8,
                CircleAvatar(
                  backgroundColor: ColorManager.lightPrimaryColor,
                  child: Text(
                    ayatFiltered["result"][index]["verse"].toString(),
                    style: const TextStyle(
                      color: ColorManager.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                AppGaps.gap8,
              ],
            ),
            // child: Text(
            //   "سورة ${getSurahNameArabic(ayatFiltered["result"][index]["surah"])} - ${getVerse(ayatFiltered["result"][index]["surah"], ayatFiltered["result"][index]["verse"], verseEndSymbol: true)}",
            //   textDirection: TextDirection.rtl,
            //   style: const TextStyle(color: Colors.black, fontSize: 17),
            // ),
          ),
        );
      },
    );
  }
}
