import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/background_widgets/background_text.dart';
import 'package:quran_broadcast_app/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/settings/providers/settings.providers.dart';
import 'package:xr_helper/xr_helper.dart';

class MainScreenTopSection extends ConsumerWidget {
  final int currentIndex;

  const MainScreenTopSection({super.key, required this.currentIndex});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bottomNavCtrl = ref.read(bottomNavControllerProvider);
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final settingsController = ref.watch(settingsControllerNotifierProvider);
    final calendarController = ref.watch(calendarControllerNotifierProvider);

    final logo = Assets.images.logo;

    final haveTitle = currentIndex != 0;

    final showBack = currentIndex == 4 ||
        currentIndex == 5 ||
        currentIndex == 6 ||
        currentIndex == 7;

    String getTitle(int index) {
      final aboutUs = settingsController.settings.value.aboutUs;

      switch (index) {
        case 1:
          return 'البث الإذاعي';
        case 2:
          return 'القرآن الكريم';
        case 4:
          return 'القبلة';
        case 5:
          return 'رسالة اليوم';
        case 6:
          return 'الدهري';
        case 7:
          return aboutUs.title.isEmpty ? 'من نحن' : aboutUs.title;
        default:
          return 'الإعدادات';
      }
    }

    return Stack(
      children: [
        Align(
            alignment: Alignment.topCenter,
            child: AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              transitionBuilder: (child, animation) {
                return ScaleTransition(scale: animation, child: child);
              },
              child: haveTitle
                  ? BackgroundText(
                      key: ValueKey(
                          getTitle(currentIndex)), // Ensure a unique key
                      text: getTitle(currentIndex),
                    )
                  : logo.image(
                      key: ValueKey(logo),
                      width: 230,
                      fit: BoxFit.cover,
                    ),
            )).paddingOnly(top: haveTitle ? 80 : 50),
        if (showBack)
          Positioned(
            top: 40,
            right: 20,
            child: CircleAvatar(
              backgroundColor: Colors.white,
              radius: 20,
              child: IconButton(
                icon: const Icon(CupertinoIcons.back),
                onPressed: () {
                  if (currentIndex == 5) {
                    calendarController.currentDateNotifier.value =
                        DateTime.now();
                  }

                  bottomNavCtrl.changeIndex(0);
                },
              ),
            ),
          ),
      ],
    );
  }
}
