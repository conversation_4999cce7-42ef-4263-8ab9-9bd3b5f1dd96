import 'dart:io';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:home_widget/home_widget.dart';
// import 'package:flutter_quran/flutter_quran.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/firebase_options.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../consts/app_constants.dart';

Future<void> initialize() async {
  try {
    // Initialize storage with error handling
    try {
      await GetStorageService.init();
      Log.i('Storage service initialized');
    } catch (e) {
      Log.e('Storage initialization failed: $e');
      rethrow; // Critical failure
    }

    // Set orientation preferences
    try {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    } catch (e) {
      Log.e('Orientation setting failed: $e');
      // Continue without orientation lock
    }

    HttpOverrides.global = MyHttpOverrides();

    // Initialize notifications with error handling
    try {
      final isAllowed = await AwesomeNotifications().isNotificationAllowed();

      if (!isAllowed && Platform.isAndroid) {
        await AwesomeNotifications().requestPermissionToSendNotifications(
          permissions: [
            NotificationPermission.Alert,
            NotificationPermission.Badge,
            NotificationPermission.Sound,
          ],
        );
      }
      Log.i('Notifications initialized');
    } catch (e) {
      Log.e('Notification initialization failed: $e');
      // Continue without notifications
    }

    await AwesomeNotifications().initialize(
      'resource://mipmap/ic_launcher',
      // debug: kDebugMode,
      [
        NotificationChannel(
          channelKey: AppConsts.azanChannelKey,
          channelName: 'Scheduled Notifications',
          soundSource: 'resource://raw/res_azan',
          channelDescription:
              'Notification channel for scheduled notifications',
          defaultColor: const Color(0xFFFFFFFF),
          ledColor: Colors.white,
          importance: NotificationImportance.Max,
          playSound: true,
        ),
        NotificationChannel(
          channelKey: AppConsts.reminderChannelKey,
          channelName: 'Reminder Notifications',
          soundSource: 'resource://raw/res_reminder',
          channelDescription: 'Notification channel for reminder notifications',
          defaultColor: const Color(0xFFFFFFFF),
          ledColor: Colors.white,
          importance: NotificationImportance.Max,
          playSound: true,
        ),
        NotificationChannel(
          channelKey: AppConsts.shroukChannelKey,
          channelName: 'Shrouk Notifications',
          // soundSource:  null,
          // Use default sound for Shrouk
          channelDescription: 'Notification channel for shrouk notifications',
          defaultColor: const Color(0xFFFFFFFF),
          ledColor: Colors.white,
          importance: NotificationImportance.Max,
          playSound: true,
        ),
      ],
    );

    // Initialize audio service with error handling to prevent SIGSEGV crashes
    try {
      await JustAudioBackground.init(
        androidNotificationChannelId: 'com.ryanheise.bg_demo.channel.audio',
        androidNotificationChannelName: 'Audio playback',
        androidNotificationOngoing: true,
        androidStopForegroundOnPause: true, // Prevent memory leaks
        preloadArtwork: false, // Reduce memory usage
      );
      Log.i('JustAudioBackground initialized successfully');
    } catch (e) {
      Log.e('JustAudioBackground initialization failed: $e');
      // Continue without audio background service if initialization fails
    }

    // await FlutterQuran().init();

    // BackgroundFetch.registerHeadlessTask(callbackDispatcherHomeWidget);
    //
    // HomeWidgetService.initialize();

    // Initialize HomeWidget with error handling
    try {
      await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);
      Log.i('HomeWidget initialized');
    } catch (e) {
      Log.e('HomeWidget initialization failed: $e');
      // Continue without home widget
    }

    // Initialize Firebase with error handling
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      Log.i('Firebase initialized');
    } catch (e) {
      Log.e('Firebase initialization failed: $e');
      // Continue without Firebase
    }

    // Initialize notification service
    try {
      NotificationService.init();
      Log.i('NotificationService initialized');
    } catch (e) {
      Log.e('NotificationService initialization failed: $e');
      // Continue without notification service
    }

    // Request battery optimization permission
    try {
      await Permission.ignoreBatteryOptimizations.request();
      Log.i('Battery optimization permission requested');
    } catch (e) {
      Log.e('Battery optimization permission failed: $e');
      // Continue without battery optimization
    }
  } catch (e) {
    Log.e('Initialization_Error: $e');
    rethrow;
  }
}
