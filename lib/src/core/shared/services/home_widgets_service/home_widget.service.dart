import 'dart:convert';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:home_widget/home_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/extensions/string_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/calendar/controllers/calendar.controller.dart';
import '../../../../features/calendar/models/calendar_model.dart';
import '../../../../features/calendar/providers/calendar.providers.dart';
import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';

class HomeWidgetService {
  static Future<void> update(BuildContext context,
      {required WidgetRef ref}) async {
    try {
      Log.w('INIT');

      final calendarController = ref.watch(calendarControllerNotifierProvider);
      final now = DateTime.now();

      // Get current day's data
      var currentDayData = calendarController.calendarByDate(now);
      var prayerTimes = currentDayData.prayerTimes;

      //! Determine the next prayer time
      var nextPrayerTime = getNextPrayerTime(prayerTimes);

      Log.w('Current time: $now');
      Log.w('Next prayer: ${nextPrayerTime.name} at ${nextPrayerTime.time}');

      //! If next prayer is Fajr and it's still before midnight but after Isha (e.g., between 19:00 and 23:59)
      if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
        final nextDay = (now.hour >= 17 && now.hour <= 23)
            ? DateTime(now.year, now.month, now.day + 1, 0, 0)
            : now;

        Log.w('Fetching next day data for Fajr: $nextDay');

        currentDayData = calendarController.calendarByDate(nextDay);

        prayerTimes = currentDayData.prayerTimes;

        nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);

        Log.i(
            'Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
      }

      currentDayData = calendarController.calendarByDate(now);
      prayerTimes = currentDayData.prayerTimes;

      await savePrayerTimes(prayerTimes, nextPrayerTime);

      _updateWidgets();
    } catch (e, s) {
      Log.e('Widget_ERROR: $e $s');
    }
  }

  static Future<void> savePrayerTimes(
      PrayerTimeModel prayerTimes, NextPrayerTime nextPrayerTime) async {
    await HomeWidget.saveWidgetData('fajr', prayerTimes.fajr.convertTo12Hour);
    await HomeWidget.saveWidgetData(
        'sunrise', prayerTimes.sunrise.convertTo12Hour);
    await HomeWidget.saveWidgetData('dhuhr', prayerTimes.dhuhr.convertTo12Hour);
    await HomeWidget.saveWidgetData('asr', prayerTimes.asr.convertTo12Hour);
    await HomeWidget.saveWidgetData(
        'maghrib', prayerTimes.maghrib.convertTo12Hour);
    await HomeWidget.saveWidgetData('isha', prayerTimes.isha.convertTo12Hour);
    await HomeWidget.saveWidgetData('nextPrayer',
        '${nextPrayerTime.name}\n${nextPrayerTime.time.formatTime}');
    await HomeWidget.saveWidgetData(
        'nextPrayerTime', nextPrayerTime.time.formatTime);
    await HomeWidget.saveWidgetData('titleText',
        nextPrayerTime.name == "الشروق" ? "موعد الشروق" : "الصلاة القادمة");
  }

  static Future<void> saveAndroidAndIOSAllPrayerTimesForWidget() async {
    try {
      if (Platform.isAndroid) {
        await _saveAndroidWidgetDataWithScheduling();
      } else if (Platform.isIOS) {
        await _saveIOSWidgetData();
      }
    } catch (e, s) {
      Log.e('Error saving all prayer times for widget: $e\n$s');
    }
  }

  static Future<void> _saveAndroidWidgetDataWithScheduling() async {
    await _saveWidgetData(30); // 30 days for Android

    // final now = DateTime.now();
    // final lastAndroidUpdate =
    //     GetStorageService.getData(key: 'lastAndroidWidgetUpdate');
    // final lastUpdateDate =
    //     lastAndroidUpdate != null ? DateTime.tryParse(lastAndroidUpdate) : null;
    //
    // // Check if we need to update (every 29 days or first time)
    // final shouldUpdate =
    //     lastUpdateDate == null || now.difference(lastUpdateDate).inDays >= 29;
    //
    // if (shouldUpdate || forceUpdate) {
    //   Log.i('Updating Android widget data (30-day schedule)');
    //   await _saveWidgetData(30); // 30 days for Android
    //   await GetStorageService.setData(
    //       key: 'lastAndroidWidgetUpdate', value: now.toIso8601String());
    // } else {
    //   final daysLeft = 29 - now.difference(lastUpdateDate!).inDays;
    //   Log.i('Android widget data still valid ($daysLeft days left)');
    // }
  }

  static Future<void> _saveIOSWidgetData() async {
    Log.i('Updating iOS widget data (7-day schedule)');
    await _saveWidgetData(7); // 7 days for iOS
  }

  static Future<void> _saveWidgetData(int daysToInclude) async {
    final calendarData = CalendarController.calendarBySummerTime;
    if (calendarData.isEmpty) {
      Log.w('No calendar data available for widget');
      return;
    }

    final Map<String, Map<String, String>> allPrayerTimes = {};
    final currentTime = DateTime.now();
    final endDate = currentTime.add(Duration(days: daysToInclude));

    for (final calendar in calendarData) {
      for (final day in calendar.days) {
        if (day.gregorianDate.isNotEmpty && day.prayerTimes.fajr.isNotEmpty) {
          final dayDate = DateTime.tryParse(day.gregorianDate);
          if (dayDate != null &&
              dayDate.isBefore(endDate) &&
              dayDate.isAfter(currentTime.subtract(const Duration(days: 1)))) {
            allPrayerTimes[day.gregorianDate] = {
              'fajr': day.prayerTimes.fajr.convertTo12Hour,
              'dhuhr': day.prayerTimes.dhuhr.convertTo12Hour,
              'asr': day.prayerTimes.asr.convertTo12Hour,
              'maghrib': day.prayerTimes.maghrib.convertTo12Hour,
              'isha': day.prayerTimes.isha.convertTo12Hour,
              'sunrise': day.prayerTimes.sunrise.convertTo12Hour,
            };
          }
        }
      }
    }

    await HomeWidget.saveWidgetData(
        'allPrayerTimes', jsonEncode(allPrayerTimes));

    final now = DateTime.now();
    final calendarController =
        ProviderContainer().read(calendarControllerNotifierProvider);
    var currentDayData = calendarController.calendarByDate(now);
    var prayerTimes = currentDayData.prayerTimes;
    var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

    if (nextPrayerTime.name == AppConsts.prayerNames[0] &&
        now.hour >= 17 &&
        now.hour <= 23) {
      final nextDay = DateTime(now.year, now.month, now.day + 1);
      currentDayData = calendarController.calendarByDate(nextDay);
      prayerTimes = currentDayData.prayerTimes;
      nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);
    }

    await savePrayerTimes(prayerTimes, nextPrayerTime);

    if (Platform.isAndroid) {
      await HomeWidget.updateWidget(name: 'AndroidAzanWidget');
      await HomeWidget.updateWidget(name: 'AndroidAzanWidget4x1');
    }
  }

  static void _updateWidgets() async {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

    if (Platform.isAndroid) {
      await HomeWidget.updateWidget(
          iOSName: AppConsts.iosWidget,
          androidName: AppConsts.androidWidget4x1);
    }
  }
}
