import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:provider/provider.dart' as provider;
import 'package:quran_broadcast_app/src/core/shared/services/connectivity_internet/connectivity_internet.dart';
import 'package:quran_broadcast_app/src/features/splash_screen/view/splash_screen.dart';
import 'package:xr_helper/xr_helper.dart';

import 'core/consts/app_constants.dart';
import 'core/shared/services/app_settings/controller/settings_controller.dart';
import 'core/shared/widgets/loading/loading_widget.dart';
import 'core/theme/theme_manager.dart';

class BaseApp extends HookConsumerWidget {
  const BaseApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerProvider);

    final theme = AppTheme(appTextTheme: GoogleFonts.alexandriaTextTheme());

    return provider.StreamProvider<NetworkStatusIOS>(
      create: (context) =>
          NetworkStatusIOSService().networkStatusController.stream,
      initialData: NetworkStatusIOS.Online,
      child: provider.StreamProvider<NetworkStatus>(
          create: (context) =>
              NetworkStatusService().networkStatusController.stream,
          initialData: NetworkStatus.Online,
          child: ScreenUtilInit(
            designSize: const Size(392.72727272727275, 800.7272727272727),
            // Optimize rendering to prevent Impeller crashes
            minTextAdapt: true,
            splitScreenMode: true,
            child: BaseMaterialApp(
              title: AppConsts.appName,
              locale: settingsController.locale,
              supportedLocales: AppConsts.supportedLocales,
              localizationsDelegates: AppConsts.localizationsDelegates,
              theme: theme.appTheme(),
              loadingWidget: const LoadingWidget(),
              home: const SplashScreen(),
            ),
          )),
    );
  }
}
