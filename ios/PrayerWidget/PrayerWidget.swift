import WidgetKit
import SwiftUI

struct SimpleEntry: TimelineEntry {
    let date: Date
    let titleText: String
    let nextPrayerName: String
    let nextPrayerTime: String
    let fajr: String
    let sunrise: String
    let dhuhr: String
    let asr: String
    let maghrib: String
    let isha: String
}


struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(
            date: Date(),
            titleText: "الصلاة القادمة",
            nextPrayerName: "--",
            nextPrayerTime: "--",
            fajr: "--", sunrise: "--", dhuhr: "--", asr: "--", maghrib: "--", isha: "--"
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> Void) {
        completion(readEntry())
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<SimpleEntry>) -> Void) {
        let currentDate = Date()
        let calendar = Calendar.current

        var entries: [SimpleEntry] = []

        // Add current entry
        entries.append(readEntry(date: currentDate))

        let prayerNames = ["الفجر", "الشروق", "الظهر", "العصر", "المغرب", "العشاء"]

        // Create entries for the next 30 days using saved data
        for dayOffset in 0..<30 {
            guard let targetDate = calendar.date(byAdding: .day, value: dayOffset, to: calendar.startOfDay(for: currentDate)) else {
                continue
            }

            let prayerTimesForDay = getPrayerTimesForDate(targetDate)

            // Add entries for each prayer time of this day
            for prayerName in prayerNames {
                if let prayerTimeString = prayerTimesForDay[prayerName],
                   prayerTimeString != "--",
                   let prayerDate = parseTimeString(prayerTimeString, for: targetDate) {

                    // Only add future prayer times
                    if prayerDate > currentDate {
                        let entry = createEntryForPrayerTime(
                            date: prayerDate,
                            nextPrayer: prayerName,
                            prayerTimesForDay: prayerTimesForDay
                        )
                        entries.append(entry)
                    }
                }
            }
        }

        // Sort entries by date
        entries.sort { $0.date < $1.date }

        // If no entries, add a default one for next hour
        if entries.isEmpty {
            let nextHour = calendar.date(byAdding: .hour, value: 1, to: currentDate)!
            entries.append(readEntry(date: nextHour))
        }

        // Limit to next 50 entries to cover more days but not overwhelm the system
        if entries.count > 50 {
            entries = Array(entries.prefix(50))
        }

        print("Created \(entries.count) timeline entries for widget")

        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }

    private func readEntry(date: Date = Date()) -> SimpleEntry {
        let userDefaults = UserDefaults(suiteName: "group.qurankareem")
        let nextPrayerValue = userDefaults?.string(forKey: "nextPrayer") ?? "--\n--"
        let nextPrayerComponents = nextPrayerValue.components(separatedBy: "\n")
        let nextPrayerName = nextPrayerComponents.first ?? "--"
        let nextPrayerTime = nextPrayerComponents.last ?? "--"

        return SimpleEntry(
            date: date,
            titleText: userDefaults?.string(forKey: "titleText") ?? "الصلاة القادمة",
            nextPrayerName: nextPrayerName,
            nextPrayerTime: nextPrayerTime,
            fajr: userDefaults?.string(forKey: "fajr") ?? "--",
            sunrise: userDefaults?.string(forKey: "sunrise") ?? "--",
            dhuhr: userDefaults?.string(forKey: "dhuhr") ?? "--",
            asr: userDefaults?.string(forKey: "asr") ?? "--",
            maghrib: userDefaults?.string(forKey: "maghrib") ?? "--",
            isha: userDefaults?.string(forKey: "isha") ?? "--"
        )
    }

    private func createEntryForPrayerTime(date: Date, nextPrayer: String, prayerTimesForDay: [String: String]? = nil) -> SimpleEntry {
        let prayerTimes = prayerTimesForDay ?? getPrayerTimesForDate(date)
        let nextPrayerInfo = getNextPrayerInfo(for: date, prayerTimes: prayerTimes)

        return SimpleEntry(
            date: date,
            titleText: getTitleText(),
            nextPrayerName: nextPrayerInfo.name,
            nextPrayerTime: nextPrayerInfo.time,
            fajr: prayerTimes["الفجر"] ?? "--",
            sunrise: prayerTimes["الشروق"] ?? "--",
            dhuhr: prayerTimes["الظهر"] ?? "--",
            asr: prayerTimes["العصر"] ?? "--",
            maghrib: prayerTimes["المغرب"] ?? "--",
            isha: prayerTimes["العشاء"] ?? "--"
        )
    }

    private func getPrayerTimesFromSharedData() -> [String: String] {
        let userDefaults = UserDefaults(suiteName: "group.qurankareem")

        return [
            "الفجر": userDefaults?.string(forKey: "fajr") ?? "--",
            "الشروق": userDefaults?.string(forKey: "sunrise") ?? "--",
            "الظهر": userDefaults?.string(forKey: "dhuhr") ?? "--",
            "العصر": userDefaults?.string(forKey: "asr") ?? "--",
            "المغرب": userDefaults?.string(forKey: "maghrib") ?? "--",
            "العشاء": userDefaults?.string(forKey: "isha") ?? "--"
        ]
    }

    private func getAllPrayerTimesFromSharedData() -> [String: [String: String]] {
        let userDefaults = UserDefaults(suiteName: "group.qurankareem")

        guard let allPrayerTimesString = userDefaults?.string(forKey: "allPrayerTimes"),
              let data = allPrayerTimesString.data(using: .utf8),
              let allPrayerTimes = try? JSONSerialization.jsonObject(with: data, options: []) as? [String: [String: String]] else {
            print("Failed to load all prayer times from shared data")
            return [:]
        }

        return allPrayerTimes
    }

    private func getPrayerTimesForDate(_ date: Date) -> [String: String] {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        let dateString = formatter.string(from: date)

        let allPrayerTimes = getAllPrayerTimesFromSharedData()

        if let prayerTimesForDate = allPrayerTimes[dateString] {
            return [
                "الفجر": prayerTimesForDate["fajr"] ?? "--",
                "الشروق": prayerTimesForDate["sunrise"] ?? "--",
                "الظهر": prayerTimesForDate["dhuhr"] ?? "--",
                "العصر": prayerTimesForDate["asr"] ?? "--",
                "المغرب": prayerTimesForDate["maghrib"] ?? "--",
                "العشاء": prayerTimesForDate["isha"] ?? "--"
            ]
        }

        // Fallback to current day's data if specific date not found
        return getPrayerTimesFromSharedData()
    }

    private func getTitleText() -> String {
        let userDefaults = UserDefaults(suiteName: "group.qurankareem")
        return userDefaults?.string(forKey: "titleText") ?? "الصلاة القادمة"
    }

    private func getNextPrayerInfo(for date: Date, prayerTimes: [String: String]) -> (name: String, time: String) {
        let calendar = Calendar.current
        let prayerNames = ["الفجر", "الشروق", "الظهر", "العصر", "المغرب", "العشاء"]
        let dayStart = calendar.startOfDay(for: date)

        // Find the next prayer after the given date
        for prayerName in prayerNames {
            if let prayerTimeString = prayerTimes[prayerName],
               let prayerDate = parseTimeString(prayerTimeString, for: dayStart) {
                if prayerDate > date {
                    return (name: prayerName, time: prayerTimeString)
                }
            }
        }

        // If no prayer found for today, get tomorrow's Fajr
        let tomorrow = calendar.date(byAdding: .day, value: 1, to: dayStart)!
        let tomorrowPrayerTimes = getPrayerTimesForDate(tomorrow)

        if let fajrTime = tomorrowPrayerTimes["الفجر"], fajrTime != "--" {
            return (name: "الفجر", time: fajrTime)
        }

        return (name: "الفجر", time: "--")
    }

    private func parseTimeString(_ timeString: String, for date: Date) -> Date? {
        let formatter = DateFormatter()
        let calendar = Calendar.current

        // Try parsing with AM/PM format first (e.g., "4:20 ص" or "12:15 م")
        formatter.dateFormat = "h:mm a"
        formatter.locale = Locale(identifier: "ar_SA")

        if let time = formatter.date(from: timeString) {
            let timeComponents = calendar.dateComponents([.hour, .minute], from: time)
            return calendar.date(bySettingHour: timeComponents.hour ?? 0,
                               minute: timeComponents.minute ?? 0,
                               second: 0,
                               of: date)
        }

        // Try English AM/PM format
        formatter.locale = Locale(identifier: "en_US_POSIX")
        if let time = formatter.date(from: timeString) {
            let timeComponents = calendar.dateComponents([.hour, .minute], from: time)
            return calendar.date(bySettingHour: timeComponents.hour ?? 0,
                               minute: timeComponents.minute ?? 0,
                               second: 0,
                               of: date)
        }

        // Try 24-hour format
        formatter.dateFormat = "HH:mm"
        if let time = formatter.date(from: timeString) {
            let timeComponents = calendar.dateComponents([.hour, .minute], from: time)
            return calendar.date(bySettingHour: timeComponents.hour ?? 0,
                               minute: timeComponents.minute ?? 0,
                               second: 0,
                               of: date)
        }

        return nil
    }
}

struct PrayerWidgetEntryView: View {
    var entry: SimpleEntry

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                VStack(alignment: .trailing) {
                    Text(entry.nextPrayerName)
                        .font(.title2)
                        .foregroundColor(Color(red: 1.0, green: 0.75, blue: 0.0))
                        .bold()
                    Text(entry.nextPrayerTime)
                        .font(.title2)
                        .foregroundColor(Color(red: 1.0, green: 0.75, blue: 0.0))
                }

                Spacer()

                Text(entry.titleText)
                    .font(.title)
                    .bold()
                    .foregroundColor(.white)
            }

            Divider()
                .background(Color.white)

            HStack {
                prayerTimeView(label: "العشاء", time: entry.isha, highlight: entry.nextPrayerName == "العشاء")
                prayerTimeView(label: "المغرب", time: entry.maghrib, highlight: entry.nextPrayerName == "المغرب")
                prayerTimeView(label: "العصر", time: entry.asr, highlight: entry.nextPrayerName == "العصر")
                prayerTimeView(label: "الظهر", time: entry.dhuhr, highlight: entry.nextPrayerName == "الظهر")
                prayerTimeView(label: "الشروق", time: entry.sunrise, highlight: entry.nextPrayerName == "الشروق")
                prayerTimeView(label: "الفجر", time: entry.fajr, highlight: entry.nextPrayerName == "الفجر")
            }
            .frame(maxWidth: .infinity)
        }
        .padding(.vertical, 8)
        .background(
            Image("prayer_bg")
             .resizable()
          .aspectRatio(contentMode: .fill)
         .frame(width: UIScreen.main.bounds.width, height: .infinity)
        )
    }

private func prayerTimeView(label: String, time: String, highlight: Bool) -> some View {
    VStack(spacing: 2) {
        Text(label)
            .font(.subheadline) // Larger font size
            .fontWeight(.bold) // Always bold
            .foregroundColor(highlight ? Color(red: 1.0, green: 0.75, blue: 0.0) : .white)
            .lineLimit(1)
            .minimumScaleFactor(0.7) // Let it shrink if needed

        Text(time)
            .font(.subheadline) // Larger font size
            .fontWeight(.bold) // Always bold
            .foregroundColor(highlight ? Color(red: 1.0, green: 0.75, blue: 0.0) : .white)
            .lineLimit(1)
            .minimumScaleFactor(0.7)
    }
    .frame(maxWidth: .infinity)
 }
}

struct PrayerWidget: Widget {
    let kind: String = "PrayerWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            PrayerWidgetEntryView(entry: entry)
                .containerBackground(.clear, for: .widget)
        }
        .configurationDisplayName("Prayer Widget")
        .description("View Prayer Times")
        .supportedFamilies([.systemMedium])
    }
}


#Preview(as: .systemMedium) {
    PrayerWidget()
} timeline: {
    SimpleEntry(
        date: .now,
        titleText: "موعد الصلاة",
        nextPrayerName: "الفجر",
        nextPrayerTime: "4:20 ص",
        fajr: "4:20 ص",
        sunrise: "6:00 ص",
        dhuhr: "12:15 م",
        asr: "3:45 م",
        maghrib: "6:20 م",
        isha: "7:50 م"
    )
}

